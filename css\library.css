/* Library page specific styles - CSS variables inherited from styles.css */



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary); /* Fixed: use --text-primary */
}





/* Navbar Styles */
header {
     background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* Add grid for library cards */
.library-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 24px;
    margin-bottom: 2rem;
}

/* Enhanced Card styles for library */
.card {
    background: rgba(255,255,255,0.05);
    border-radius: 16px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(var(--neon-blue-rgb),0.15), 0 5px 15px rgba(0,0,0,0.2);
    border-color: var(--neon-blue);
    background: rgba(255,255,255,0.08);
}

.card .img-container {
    position: relative;
    width: 100%;
    height: 160px;
    overflow: hidden;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}

.card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover img {
    transform: scale(1.05);
}

.card-content {
    padding: 1.2rem;
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    flex-grow: 1;
}

.text-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.card-content h3,
.card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.card-content p,
.card-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

/* Enhanced Play Overlay */
.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.card:hover .play-overlay {
    opacity: 1;
}

.play-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    color: white;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 224, 255, 0.3);
}

.play-button:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.4), 0 8px 25px rgba(255, 0, 110, 0.4);
}

/* Card Actions Container */
.card-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    margin-top: auto;
}

/* Action Button Variants */
.action-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
    border-color: var(--neon-blue);
}

.action-btn.primary {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    color: white;
}

.action-btn.primary:hover {
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.3);
}

/* Deezer Search Results Styling */
.search-results-container {
    margin: 2rem 0;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.search-results-header {
    text-align: center;
    margin-bottom: 2rem;
}

.search-results-header h2 {
    color: var(--text-primary);
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.deezer-badge {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-info {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
}

.deezer-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.deezer-result-card {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.deezer-result-card::before {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 50%;
    z-index: 2;
}

.deezer-result-card:hover {
    border-color: #ff6b35;
    box-shadow: 0 15px 35px rgba(255, 107, 53, 0.15), 0 5px 15px rgba(0,0,0,0.2);
}

.duration {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Mobile responsiveness for search results */
@media (max-width: 768px) {
    .search-results-container {
        margin: 1rem 0;
        padding: 1.5rem;
    }

    .search-results-header h2 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .deezer-results-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
}

/* Section title */
.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* Add margin to mini player */
.mini-player {
    margin-bottom: 0;
}

/* Hero section refinement */
.library-hero {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 4rem 2rem 3rem 2rem;
    margin-top: 120px; /* Increased to push hero below navbar */
    margin-bottom: 3rem;
    border-radius: 18px;
    background: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%, rgba(111,0,255,0.08) 100%);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    min-height: 320px;
    overflow: hidden;
}

.library-hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
        radial-gradient(circle at 60% 40%, rgba(111,0,255,0.12) 0%, transparent 70%),
        linear-gradient(120deg, rgba(0,224,255,0.08) 0%, rgba(255,0,110,0.10) 80%, rgba(111,0,255,0.08) 100%);
    background-size: 100% 100%, 200% 200%;
    opacity: 0.7;
    z-index: 1;
    pointer-events: none;
    border-radius: inherit;
    animation: heroGradientMove 15s ease-in-out infinite;
}

@keyframes heroGradientMove {
    0%, 100% {
        background-position: 0% 0%, 0% 50%;
    }
    25% {
        background-position: 0% 0%, 100% 50%;
    }
    50% {
        background-position: 0% 0%, 100% 100%;
    }
    75% {
        background-position: 0% 0%, 0% 100%;
    }
}

.particles-container {
    position: absolute;
    inset: 0;
    z-index: 2;
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.particle {
    position: absolute;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, var(--neon-blue), var(--cosmic-pink), transparent 70%);
    border-radius: 50%;
    opacity: 0.2; /* Subtle but visible */
    box-shadow:
        0 0 15px 5px rgba(111,0,255,0.15),
        0 0 30px 10px rgba(0,224,255,0.08),
        0 0 20px 7px rgba(255,0,110,0.06);
    animation: particleDrift linear infinite;
    will-change: transform, opacity;
}

@keyframes particleDrift {
    0% {
        opacity: 0;
        transform: translate(0, 0) scale(0.8);
    }
    15% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
        transform: translate(var(--dx, 30px), var(--dy, 30px)) scale(1.1);
    }
    85% {
        opacity: 0.6;
    }
    100% {
        opacity: 0;
        transform: translate(0, 0) scale(0.8);
    }
}

.library-hero-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    position: relative;
    z-index: 3;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    margin-bottom: 2em;
    line-height: 1.6;
    position: relative;
    z-index: 3;
}

.library-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    position: relative;
    z-index: 3;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    padding: 1.5rem 2.2rem;
    border-radius: 16px;
    text-align: center;
    transition: transform 0.3s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.04);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    /* Add gradient color to icon */
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb),0.28);
    /* Increased text-shadow opacity for more glow */
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: #fff; /* Ghost white/white */
    display: block;
    margin-bottom: 0.3rem;
    text-shadow: 0 0 12px rgba(255,255,255,0.7), 0 0 24px rgba(111,0,255,0.18);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 0.02em;
}

/* Library Search Section Styles */
.library-search-section {
    background: linear-gradient(120deg, rgba(19,21,26,0.92) 0%, rgba(26,29,36,0.90) 70%, rgba(111,0,255,0.07) 100%);
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    padding: 2rem 2rem 1.5rem 2rem;
    max-width: 700px;
    margin: 0 auto 2.5rem auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.library-search-bar {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 500px;
    background: rgba(255,255,255,0.05);
    border-radius: 30px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid rgba(255,255,255,0.10);
    padding: 0.5rem 0.5rem 0.5rem 1.2rem;
    margin: 0 auto;
    position: relative;
    transition: box-shadow 0.2s, border 0.2s;
}

.library-search-bar:focus-within {
    border: 1.5px solid var(--cosmic-pink);
    box-shadow: 0 0 0 2px rgba(255,0,110,0.18), 0 0 12px 2px rgba(0,224,255,0.12);
}

.library-search-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 1.1rem;
    padding: 0.6rem 2.2rem 0.6rem 0.2rem;
    border-radius: 30px;
}

.library-search-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.library-search-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-left: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
}

.library-search-btn:hover,
.library-search-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.08);
}

.library-search-clear {
    position: absolute;
    right: 55px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s;
    z-index: 2;
}

.library-search-clear:hover {
    color: var(--cosmic-pink);
}

/* Search Filters */
.search-filters {
    display: flex;
    gap: 0.8rem;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 0.7rem;
    margin-bottom: 0.5rem;
    padding: 0 1rem;
    box-sizing: border-box;
}

.filter-btn {
    padding: 0.7rem 1.5rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    font-size: 1rem;
}

.filter-btn:hover,
.filter-btn:focus {
    background: rgba(255, 255, 255, 0.12);
    color: var(--neon-blue);
    outline: none;
}

.filter-btn.active {
    background: var(--button-gradient);
    color: #fff;
    border-color: transparent;
    box-shadow: 0 0 15px rgba(255, 0, 110, 0.3);
}

/* Results Count & Container */
.results-count {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    min-height: 1.2em;
    text-align: left;
    width: 100%;
    max-width: 500px;
}

.results-container {
    margin-top: 0.5rem;
    min-height: 80px;
    background: rgba(255,255,255,0.02);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 1.2rem;
    color: var(--text-primary);
    font-size: 1.08rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 500px;
}

.empty-results {
    color: var(--text-secondary);
    font-size: 1.08rem;
    text-align: center;
    opacity: 0.7;
}

/* Unify card grid layout for all main card containers */
.library-cards,
.liked-songs-cards,
.recently-played-cards,
.top-artists-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
    justify-items: center;
    align-items: stretch;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2rem;
}

/* Card styles for library */
.card {
    background: rgba(255,255,255,0.05);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
}

.card:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 10px 24px rgba(var(--neon-blue-rgb),0.10), 0 2px 10px rgba(0,0,0,0.13);
    border-color: var(--neon-blue);
}

.card img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.card-content {
    padding: 1rem;
    width: 100%;
    text-align: center;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.3em;
}

.card-subtitle {
    font-size: 0.98em;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Section title */
.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* Add margin to mini player */
.mini-player {
    margin-bottom: 0;
}



.library-hero-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    position: relative;
    z-index: 3;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    margin-bottom: 2em;
    line-height: 1.6;
    position: relative;
    z-index: 3;
}

.library-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    position: relative;
    z-index: 3;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem 2.2rem;
    border-radius: 16px;
    text-align: center;
    transition: transform 0.3s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.04);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    /* Add gradient color to icon */
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb),0.28);
    /* Increased text-shadow opacity for more glow */
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: #fff; /* Ghost white/white */
    display: block;
    margin-bottom: 0.3rem;
    text-shadow: 0 0 12px rgba(255,255,255,0.7), 0 0 24px rgba(111,0,255,0.18);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 0.02em;
}

/* Library Search Section Styles */
.library-search-section {
    background: linear-gradient(120deg, rgba(19,21,26,0.92) 0%, rgba(26,29,36,0.90) 70%, rgba(111,0,255,0.07) 100%);
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    padding: 2rem 2rem 1.5rem 2rem;
    max-width: 700px;
    margin: 0 auto 2.5rem auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.library-search-bar {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 500px;
    background: rgba(255,255,255,0.05);
    border-radius: 30px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid rgba(255,255,255,0.10);
    padding: 0.5rem 0.5rem 0.5rem 1.2rem;
    margin: 0 auto;
    position: relative;
    transition: box-shadow 0.2s, border 0.2s;
}

.library-search-bar:focus-within {
    border: 1.5px solid var(--cosmic-pink);
    box-shadow: 0 0 0 2px rgba(255,0,110,0.18), 0 0 12px 2px rgba(0,224,255,0.12);
}

.library-search-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 1.1rem;
    padding: 0.6rem 2.2rem 0.6rem 0.2rem;
    border-radius: 30px;
}

.library-search-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.library-search-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-left: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
}

.library-search-btn:hover,
.library-search-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.08);
}

.library-search-clear {
    position: absolute;
    right: 55px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s;
    z-index: 2;
}

.library-search-clear:hover {
    color: var(--cosmic-pink);
}

/* Search Filters */
.search-filters {
    display: flex;
    gap: 0.8rem;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 0.7rem;
    margin-bottom: 0.5rem;
    padding: 0 1rem;
    box-sizing: border-box;
}

.filter-btn {
    padding: 0.7rem 1.5rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    font-size: 1rem;
}

.filter-btn:hover,
.filter-btn:focus {
    background: rgba(255, 255, 255, 0.12);
    color: var(--neon-blue);
    outline: none;
}

.filter-btn.active {
    background: var(--button-gradient);
    color: #fff;
    border-color: transparent;
    box-shadow: 0 0 15px rgba(255, 0, 110, 0.3);
}

/* Results Count & Container */
.results-count {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    min-height: 1.2em;
    text-align: left;
    width: 100%;
    max-width: 500px;
}

.results-container {
    margin-top: 0.5rem;
    min-height: 80px;
    background: rgba(255,255,255,0.02);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 1.2rem;
    color: var(--text-primary);
    font-size: 1.08rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 500px;
}

.empty-results {
    color: var(--text-secondary);
    font-size: 1.08rem;
    text-align: center;
    opacity: 0.7;
}

/* Unify card grid layout for all main card containers */
.library-cards,
.liked-songs-cards,
.recently-played-cards,
.top-artists-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
    justify-items: center;
    align-items: stretch;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2rem;
}

/* Card styles for library */
.card {
    background: rgba(255,255,255,0.05);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.15);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
}

.card:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 10px 24px rgba(var(--neon-blue-rgb),0.10), 0 2px 10px rgba(0,0,0,0.13);
    border-color: var(--neon-blue);
}

.card img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.card-content {
    padding: 1rem;
    width: 100%;
    text-align: center;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.3em;
}

.card-subtitle {
    font-size: 0.98em;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Section title */
.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* Add margin to mini player */
.mini-player {
    margin-bottom: 0;
}



.library-hero-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    position: relative;
    z-index: 3;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    margin-bottom: 2em;
    line-height: 1.6;
    position: relative;
    z-index: 3;
}

.library-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    position: relative;
    z-index: 3;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem 2.2rem;
    border-radius: 16px;
    text-align: center;
    transition: transform 0.3s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-item:hover {
    transform: translateY(-5px) scale(1.04);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    /* Add gradient color to icon */
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb),0.28);
    /* Increased text-shadow opacity for more glow */
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: #fff; /* Ghost white/white */
    display: block;
    margin-bottom: 0.3rem;
    text-shadow: 0 0 12px rgba(255,255,255,0.7), 0 0 24px rgba(111,0,255,0.18);
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 0.02em;
}

/* Library Search Section Styles */
.library-search-section {
    background: linear-gradient(120deg, rgba(19,21,26,0.92) 0%, rgba(26,29,36,0.90) 70%, rgba(111,0,255,0.07) 100%);
    border-radius: 18px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.08);
    padding: 2rem 2rem 1.5rem 2rem;
    max-width: 700px;
    margin: 0 auto 2.5rem auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.library-search-bar {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 500px;
    background: rgba(255,255,255,0.05);
    border-radius: 30px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    border: 1px solid rgba(255,255,255,0.10);
    padding: 0.5rem 0.5rem 0.5rem 1.2rem;
    margin: 0 auto;
    position: relative;
    transition: box-shadow 0.2s, border 0.2s;
}

.library-search-bar:focus-within {
    border: 1.5px solid var(--cosmic-pink);
    box-shadow: 0 0 0 2px rgba(255,0,110,0.18), 0 0 12px 2px rgba(0,224,255,0.12);
}

.library-search-input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 1.1rem;
    padding: 0.6rem 2.2rem 0.6rem 0.2rem;
    border-radius: 30px;
}

.library-search-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.library-search-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-left: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow-button);
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
}

.library-search-btn:hover,
.library-search-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.08);
}

.library-search-clear {
    position: absolute;
    right: 55px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s;
    z-index: 2;
}

.library-search-clear:hover {
    color: var(--cosmic-pink);
}

/* Search Filters */
.search-filters {
    display: flex;
    gap: 0.8rem;
    justify-content: center;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 0.7rem;
    margin-bottom: 0.5rem;
    padding: 0 1rem;
    box-sizing: border-box;
}

.filter-btn {
    padding: 0.7rem 1.5rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    font-size: 1rem;
}

.filter-btn:hover,
.filter-btn:focus {
    background: rgba(255, 255, 255, 0.12);
    color: var(--neon-blue);
    outline: none;
}

.filter-btn.active {
    background: var(--button-gradient);
    color: #fff;
    border-color: transparent;
    box-shadow: 0 0 15px rgba(255, 0, 110, 0.3);
}

/* Results Count & Container */
.results-count {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
    min-height: 1.2em;
    text-align: left;
    width: 100%;
    max-width: 500px;
}

.results-container {
    margin-top: 0.5rem;
    min-height: 80px;
    background: rgba(255,255,255,0.02);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 1.2rem;
    color: var(--text-primary);
    font-size: 1.08rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 500px;
}

.empty-results {
    color: var(--text-secondary);
    font-size: 1.08rem;
    text-align: center;
    opacity: 0.7;
}

/* --- Option 6: Improved mobile tweaks --- */
@media (max-width: 600px) {
    .library-section,
    .library-search-section,
    .library-hero {
        padding-left: 2vw;
        padding-right: 2vw;
    }
    .section-header .section-title {
        font-size: 1.1em;
    }
    .create-playlist-btn,
    .play-all-btn {
        width: 100%;
        justify-content: center;
        font-size: 1em;
        padding: 0.7em 0;
        min-width: 0;
    }
    .library-cards,
    .liked-songs-cards,
    .recently-played-cards,
    .top-artists-cards {
        grid-template-columns: 1fr;
        gap: 10px;
        max-width: 100%;
    }
    .card {
        max-width: 100%;
        min-width: 0;
    }
}

/* --- Option 7: Minor cleanup --- */
/* Remove duplicate/old .library-cards.centered and similar rules if present */
/* ...existing code... */

/* Consistent section container for library sections */
.library-section {
    background: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%, rgba(111,0,255,0.08) 100%);
    border-radius: 18px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.18);
    padding: 2.5rem 2rem 2rem 2rem;
    margin: 2.5rem auto 2.5rem auto;
    max-width: 1100px;
    width: 100%;
    position: relative;
    z-index: 1;
    /* Add subtle border for depth */
    border: 1.5px solid rgba(255,255,255,0.10);
    transition: box-shadow 0.3s, background 0.3s;
}

/* Section header and divider consistency */
.section-header {
    text-align: center;
    margin-bottom: 1.2rem;
    position: relative;
}
.section-header .section-title {
    font-size: 2em;
    font-weight: 800;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    margin-bottom: 0.3em;
}
.section-divider {
    width: 100%;
    max-width: 700px;
    height: 2px;
    margin: 0.5em auto 1.2em auto;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    opacity: 0.12;
    border-radius: 2px;
}

/* Section description consistency */
.section-description {
    color: var(--text-secondary);
    font-size: 1.05em;
    margin-bottom: 1.2rem;
    margin-top: 0;
    text-align: center;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive adjustments for section containers */
@media (max-width: 1200px) {
    .library-section {
        max-width: 98vw;
        padding: 1.5rem 0.5rem 1rem 0.5rem;
    }
    .section-header .section-title {
        font-size: 1.3em;
    }
}

/* Widen main containers for full-width look */
.library-section,
.library-search-section,
.library-hero {
    max-width: none;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    border-radius: 0;
    padding-left: 3vw;
    padding-right: 3vw;
}

/* Remove max-width from cards grids for full stretch */
.library-cards,
.liked-songs-cards,
.recently-played-cards,
.top-artists-cards {
    max-width: none;
    margin-left: 0;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
}

/* Search Results Styling */
.search-results-container {
    display: none;
    padding: 2rem 3vw;
    background: rgba(255,255,255,0.02);
    border-radius: 12px;
    margin: 2rem 0;
}

.search-results-header h2 {
    color: #fff;
    font-size: 1.8rem;
    margin-bottom: 2rem;
    text-align: center;
}

.search-results-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.search-tracks-section,
.search-playlists-section {
    width: 100%;
}

.search-tracks-section h3,
.search-playlists-section h3 {
    color: #fff;
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    padding-left: 1rem;
    border-left: 3px solid #00d4ff;
}

.search-tracks-grid,
.search-playlists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
    justify-items: center;
    align-items: stretch;
}

.no-results {
    color: #888;
    text-align: center;
    font-style: italic;
    padding: 2rem;
    grid-column: 1 / -1;
}

.search-track-card,
.search-playlist-card {
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.search-track-card:hover,
.search-playlist-card:hover {
    background: rgba(255,255,255,0.08);
    border-color: rgba(0,212,255,0.3);
    transform: translateY(-5px);
}

/* Consistent card hover/focus effect */
.card,
.playlist-card,
.liked-song-card,
.recently-played-card,
.top-artist-card {
    transition: box-shadow 0.25s, transform 0.25s, background 0.25s;
}
.card:focus-within,
.card:hover,
.playlist-card:focus-within,
.playlist-card:hover,
.liked-song-card:focus-within,
.liked-song-card:hover,
.recently-played-card:focus-within,
.recently-played-card:hover,
.top-artist-card:focus-within,
.top-artist-card:hover {
    box-shadow: 0 8px 24px rgba(0,224,255,0.13), 0 2px 10px rgba(255,0,110,0.10);
    background: linear-gradient(120deg, rgba(0,224,255,0.13) 0%, rgba(255,0,110,0.13) 100%);
    transform: translateY(-4px) scale(1.02);
    z-index: 2;
    outline: none;
}

/* Responsive: keep some padding on small screens */
@media (max-width: 900px) {
    .library-section,
    .library-search-section,
    .library-hero {
        padding-left: 1vw;
        padding-right: 1vw;
    }
}

/* Playlist Card Enhancements */
.playlist-card {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    outline: none;
    transition: box-shadow 0.2s, transform 0.2s;
    width: 100%;
    max-width: 320px; /* Match liked-song-card max width */
    min-width: 220px;
}
.playlist-card:focus {
    box-shadow: 0 0 0 3px var(--neon-blue), 0 2px 12px rgba(0,224,255,0.18);
    z-index: 2;
}
.playlist-card .img-container {
    width: 100%;
    height: 160px;
    position: relative;
    overflow: hidden;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.13);
}
.playlist-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    filter: brightness(0.93);
}
.playlist-card:hover img,
.playlist-card:focus img {
    transform: scale(1.07);
    filter: brightness(1.08) contrast(1.08);
}

/* Play overlay on hover/focus */
.play-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0,0,0,0.38);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.25s;
    z-index: 2;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}
.playlist-card:hover .play-overlay,
.playlist-card:focus .play-overlay {
    opacity: 1;
    pointer-events: auto;
}
.play-button {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,224,255,0.18);
    cursor: pointer;
    transition: background 0.2s, transform 0.2s;
}
.play-button:hover,
.play-button:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.1);
    outline: none;
}

/* Playlist meta info */
.playlist-meta {
    display: flex;
    gap: 0.7em;
    justify-content: center;
    align-items: center;
    margin-top: 0.7em;
    font-size: 0.98em;
}
.playlist-count {
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.3em;
    font-size: 0.97em;
}
.playlist-count i {
    color: var(--neon-blue);
    font-size: 1em;
}
.playlist-tag {
    background: rgba(0,224,255,0.09);
    color: var(--neon-blue);
    border-radius: 12px;
    padding: 0.15em 0.7em;
    font-size: 0.93em;
    font-weight: 600;
    letter-spacing: 0.01em;
}

/* Responsive tweaks */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.7em;
    }
    .section-description {
        font-size: 0.98em;
    }
    .playlist-meta {
        flex-direction: column;
        gap: 0.3em;
    }
}
@media (max-width: 480px) {
    .create-playlist-btn {
        font-size: 0.95em;
        padding: 0.5em 1em;
    }
    .playlist-meta {
        font-size: 0.93em;
    }
}

/* Liked Songs Card Content Consistency */
.liked-song-card .card-content {
    padding: 1rem;
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.7em;
}

.liked-song-card .text-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0.3em;
}

.liked-song-card .card-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.2em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90%;
}

.liked-song-card .card-subtitle {
    font-size: 0.98em;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0;
    max-width: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Play Now button for liked songs */
.play-now-btn {
    margin-top: auto;
    display: inline-block;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    color: #fff;
    padding: 8px 16px;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb), 0.5);
    text-align: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.play-now-btn:hover,
.play-now-btn:focus {
    transform: translateY(-2px);
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    text-shadow:
        0 0 10px rgba(var(--neon-blue-rgb), 0.8),
        0 0 20px rgba(var(--cosmic-pink-rgb), 0.4);
    outline: 2px solid var(--cosmic-pink);
    outline-offset: 2px;
}

/* Recently Played Cards Grid */
.recently-played-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    max-width: 950px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2rem;
    gap: 24px;
    justify-items: center;
}

/* Recently Played Card (inherits from .card, .liked-song-card, .playlist-card) */
.recently-played-card {
    width: 100%;
    max-width: 320px;
    min-width: 220px;
    /* Use same style as .playlist-card/.liked-song-card for consistency */
}

/* Responsive: match other card breakpoints */
@media (max-width: 768px) {
    .recently-played-cards {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 14px;
    }
    .recently-played-card {
        max-width: 200px;
        min-width: 120px;
    }
    .recently-played-card .img-container {
        height: 100px;
    }
    .recently-played-card .card-content {
        padding: 0.7rem;
    }
}

@media (max-width: 480px) {
    .recently-played-cards {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    .recently-played-card {
        max-width: 100%;
        min-width: 0;
    }
    .recently-played-card .img-container {
        height: 80px;
    }
    .recently-played-card .card-content {
        padding: 0.5rem;
    }
}

/* Top Artists Cards Grid */
.top-artists-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    max-width: 950px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 2rem;
    gap: 24px;
    justify-items: center;
}

/* Top Artist Card (inherits from .card) */
.top-artist-card {
    width: 100%;
    max-width: 320px;
    min-width: 220px;
    /* Use same style as .playlist-card/.liked-song-card for consistency */
}

/* Responsive: match other card breakpoints */
@media (max-width: 768px) {
    .top-artists-cards {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 14px;
    }
    .top-artist-card {
        max-width: 200px;
        min-width: 120px;
    }
    .top-artist-card .img-container {
        height: 100px;
    }
    .top-artist-card .card-content {
        padding: 0.7rem;
    }
}

@media (max-width: 480px) {
    .top-artists-cards {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    .top-artist-card {
        max-width: 100%;
        min-width: 0;
    }
    .top-artist-card .img-container {
        height: 80px;
    }
    .top-artist-card .card-content {
        padding: 0.5rem;
    }
}

.filter-select:hover {
    background: rgba(255, 255, 255, 0.1);
}

.filter-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--neon-blue);
}

/* Responsive improvements for hero */
@media (max-width: 768px) {
    .library-hero {
        padding: 2.5rem 0.5rem 1.5rem 0.5rem;
        margin-top: 100px; /* Still enough space below navbar on tablet */
    }
    .library-hero-title {
        font-size: 2rem;
    }
    .hero-subtitle {
        font-size: 1.1rem;
    }
    .library-stats {
        flex-direction: column;
        gap: 1.2rem;
    }
    .stat-item {
        padding: 1rem 1.2rem;
    }
}

@media (max-width: 480px) {
    .library-hero {
        padding: 1.5rem 0.2rem 1rem 0.2rem;
        margin-top: 80px; /* For mobile, still clear of navbar */
    }
    .library-hero-title {
        font-size: 1.3rem;
    }
    .stat-item {
        padding: 0.8rem 0.5rem;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .library-cards {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 14px;
    }
    .card img {
        height: 100px;
    }
    .card-content {
        padding: 0.7rem;
    }
}

@media (max-width: 480px) {
    .library-cards {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    .card img {
        height: 80px;
    }
    .card-content {
        padding: 0.5rem;
    }
}

/* Centering styles for Your Playlists section */
.centered {
    text-align: center !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Ensure .section-header.centered uses flex and centers children */
.section-header.centered {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.7em;
    text-align: center;
}

/* Center the Create Playlist button and Play All button */
.create-playlist-btn,
.play-all-btn {
    margin-left: 0;
    margin-right: 0;
}

/* Center the cards grid for this section and ensure grid layout */
.library-cards.centered {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
    justify-items: center;
    align-items: stretch;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive: stack cards on small screens */
@media (max-width: 768px) {
    .library-cards.centered {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 14px;
        max-width: 100%;
    }
}
@media (max-width: 480px) {
    .library-cards.centered {
        grid-template-columns: 1fr;
        gap: 10px;
        max-width: 100%;
    }
}
.playlist-sort {
    display: flex;
    align-items: center;
    gap: 0.5em;
    margin-top: 0.5em;
    justify-content: center;
    margin-bottom: 1.2em;
}

.playlist-sort select {
    background: rgba(19,21,26,0.95);
    color: var(--text-primary);
    border: 1.5px solid rgba(255,255,255,0.18);
    border-radius: 18px;
    padding: 0.5em 2.2em 0.5em 1em;
    font-size: 1em;
    font-weight: 600;
    outline: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    transition: border 0.2s, background 0.2s, box-shadow 0.2s;
    appearance: none;
    cursor: pointer;
    min-width: 150px;
    position: relative;
}

.playlist-sort select:focus {
    border: 1.5px solid var(--neon-blue);
    background: rgba(26,29,36,0.98);
    box-shadow: 0 0 0 2px var(--neon-blue);
}

.playlist-sort select:hover {
    background: rgba(19,21,26,1);
    border-color: var(--cosmic-pink);
}

.playlist-sort::after {
    content: "\f078";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    color: var(--neon-blue);
    position: absolute;
    right: 1.2em;
    pointer-events: none;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.95em;
    z-index: 2;
    display: none; /* fallback for browsers not supporting ::after on flex */
}

/* Responsive: make dropdown full width on mobile */
@media (max-width: 600px) {
    .playlist-sort select {
        width: 100%;
        min-width: 0;
        font-size: 1em;
        padding: 0.6em 1em;
    }
}

/* Playlist Details Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;

  height: 100vh;
  overflow: auto;
  background: rgba(0,0,0,0.7);
  justify-content: center;
  align-items: center;
}
.modal-content {
  background: var(--background-secondary, #222);
  margin: 5% auto;
  padding: 2rem;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  color: var(--text-primary, #fff);
  box-shadow: var(--shadow-card, 0 8px 32px rgba(56, 12, 97, 0.15));
  position: relative;
}
.modal .close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 2rem;
  color: var(--text-secondary, #aaa);
  cursor: pointer;
  background: none;
  border: none;
}
#modal-track-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0 0 0;
}
#modal-track-list li {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#modal-track-list button {
  background: var(--accent-color, #FF006E);
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.3em 0.7em;
  cursor: pointer;
  font-size: 1em;
}
#modal-track-list button:hover {
  background: var(--neon-blue, #00E0FF);
}

/* ===== LIBRARY PAGE BUTTON CUSTOMIZATIONS ===== */
/* Note: Main button styles now inherit from standardized system in styles.css */

/* Library-specific button overrides */
.library-section .button {
    min-width: 110px;
    border-radius: 10px;
}

/* Library-specific button variants - now use standardized system */
.create-playlist-btn {
    /* Extends btn btn-primary btn-md */
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    padding: 0.8rem 1.8rem;
    border-radius: 30px;
    box-shadow: 0 4px 15px rgba(255, 0, 110, 0.2);
}

.create-playlist-btn:hover {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 25px rgba(255, 0, 110, 0.4);
}

.play-all-btn {
    /* Extends btn btn-primary btn-md */
    background: linear-gradient(45deg, #4CAF50, var(--neon-blue));
    padding: 0.8rem 1.8rem;
    border-radius: 30px;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

.play-all-btn:hover {
    background: linear-gradient(45deg, var(--neon-blue), #4CAF50);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 25px rgba(76, 175, 80, 0.4);
}

/* ===== ENHANCED MOBILE RESPONSIVENESS FIXES ===== */
@media (max-width: 768px) {
    /* Center all main containers */
    .library-container {
        max-width: 100%;
        margin: 0 auto;
        padding: 1rem;
    }

    /* Center library header */
    .library-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    /* Center library stats */
    .library-stats {
        max-width: 400px;
        margin: 0 auto 2rem auto;
        justify-content: center;
    }

    /* Center library controls */
    .library-controls {
        max-width: 500px;
        margin: 0 auto;
        align-items: center;
    }

    /* Center search container */
    .search-container {
        max-width: 400px;
        margin: 0 auto;
    }

    /* Center filter buttons */
    .filter-buttons {
        justify-content: center;
    }

    /* Center all card grids */
    .library-cards,
    .playlist-cards,
    .liked-songs-cards,
    .recently-played-cards,
    .top-artists-cards {
        max-width: 500px;
        margin: 0 auto;
        justify-content: center;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    /* Center individual cards */
    .card,
    .playlist-card,
    .liked-song-card,
    .recently-played-card,
    .top-artist-card {
        margin: 0 auto;
        max-width: 300px;
        width: 100%;
    }

    /* Center card content */
    .card h3,
    .card p,
    .playlist-card h3,
    .playlist-card p {
        text-align: center;
    }

    /* Center buttons in cards */
    .card .button,
    .playlist-card .button {
        margin: 0 auto;
        display: block;
        width: fit-content;
    }

    /* Center sections */
    .library-section {
        text-align: center;
        margin-bottom: 3rem;
    }

    .library-section h2 {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    /* Improve spacing */
    .section-header {
        justify-content: center;
        text-align: center;
    }

    /* Center hero content */
    .library-hero {
        text-align: center;
    }

    /* Better mobile card hover states */
    .card:hover,
    .playlist-card:hover {
        transform: translateY(-2px) scale(1.02);
    }
}

// Enhanced Library Page Management System with Mobile Optimization
class LibraryManager {
    constructor() {
        this.searchTimeout = null;
        this.currentFilter = 'all';
        this.sortOrder = 'recent';
        this.apiBase = null; // GitHub Pages mode - no backend
        this.deezerService = window.deezerService;
        this.jamendoService = window.jamendoService;
        this.hybridMusicService = window.hybridMusicService;
        this.bansheeUtils = window.bansheeUtils;

        // Local storage for GitHub Pages demo
        this.storageKey = 'banshee_library_data';
        this.initializeLocalStorage();

        // Mobile optimization properties
        this.isMobile = window.innerWidth <= 768;
        this.touchStartY = 0;
        this.isScrolling = false;
        this.lastScrollTop = 0;

        this.init();
    }

    async init() {
        this.bindElements();
        this.bindEvents();
        this.setupMobileOptimizations();
        this.ensureParticles(); // Ensure particles always present
        this.animateStats();
        this.refreshCardEventBindings(); // Initial card event binding

        // Load data from backend with performance monitoring
        await this.loadBackendData();

        // Setup progressive loading for mobile
        if (this.isMobile) {
            this.setupLazyLoading();
        }
    }

    bindElements() {
        // Search elements
        this.searchInput = document.querySelector('.library-search-input');
        this.searchClear = document.querySelector('.library-search-clear');

        // Navigation tabs
        this.navTabs = document.querySelectorAll('.nav-tab');

        // Action buttons
        this.shuffleAllBtn = document.getElementById('shuffleAllBtn');
        this.importMusicBtn = document.getElementById('importMusicBtn');
        this.createPlaylistBtn = document.querySelector('.create-playlist-btn');
        this.playAllBtn = document.querySelector('.play-all-btn');

        // Statistics elements
        this.statNumbers = document.querySelectorAll('.stat-number');

        // ARIA live region
        this.liveRegion = document.getElementById('aria-live-region') || this.createLiveRegion();
    }

    bindEvents() {
        // Search functionality
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        if (this.searchClear) {
            this.searchClear.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // Navigation tabs
        this.navTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const section = e.target.dataset.section;
                this.switchSection(section);
            });
        });

        // Action buttons
        if (this.shuffleAllBtn) {
            this.shuffleAllBtn.addEventListener('click', () => {
                this.shuffleAll();
            });
        }

        if (this.importMusicBtn) {
            this.importMusicBtn.addEventListener('click', () => {
                this.importMusic();
            });
        }

        if (this.createPlaylistBtn) {
            this.createPlaylistBtn.addEventListener('click', () => {
                this.createPlaylist();
            });
        }

        if (this.playAllBtn) {
            this.playAllBtn.addEventListener('click', () => {
                this.playAllLikedSongs();
            });
        }

        // Card interactions
        this.libraryCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.handleCardClick(e, card);
            });

            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleCardClick(e, card);
                }
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.searchInput?.focus();
            }
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.createPlaylist();
            }
        });

        // Filter buttons
        const filterButtons = document.querySelectorAll('.filter-btn');
        filterButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Update active filter button
                filterButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // Update current filter
                this.currentFilter = btn.dataset.filter;

                // Re-run search with current filter if there's a search query
                const currentQuery = this.searchInput?.value.trim();
                if (currentQuery) {
                    this.performSearch(currentQuery);
                } else {
                    this.applyFilter(this.currentFilter);
                }
            });
        });

        // Prevent form submission for search
        const searchForm = document.querySelector('.library-search-bar');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                if (this.searchInput) {
                    this.handleSearch(this.searchInput.value);
                }
            });
        }
    }

    // Ensure particles are present and animated
    ensureParticles() {
        const heroSection = document.querySelector('.library-hero');
        if (!heroSection) return;

        let container = heroSection.querySelector('.particles-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'particles-container';
            container.setAttribute('aria-hidden', 'true');
            heroSection.appendChild(container);
        }

        // If not enough particles, add them
        const currentParticles = container.querySelectorAll('.particle').length;
        const targetParticles = this.isMobile ? 10 : 15; // Fewer particles on mobile
        const needed = targetParticles - currentParticles;

        for (let i = 0; i < needed; i++) {
            const p = document.createElement('div');
            p.className = 'particle';
            container.appendChild(p);
        }

        // Remove excess particles if any
        if (currentParticles > targetParticles) {
            const particles = container.querySelectorAll('.particle');
            for (let i = targetParticles; i < particles.length; i++) {
                particles[i].remove();
            }
        }

        // Animate particles
        this.initializeParticles();
    }

    // Re-apply card event listeners and intersection observer after rendering
    refreshCardEventBindings() {
        const cards = document.querySelectorAll('.library-card, .playlist-card, .liked-song-card, .artist-card');
        // Remove previous listeners by cloning
        cards.forEach(card => {
            const clone = card.cloneNode(true);
            card.parentNode.replaceChild(clone, card);
        });
        // Re-query after cloning
        const freshCards = document.querySelectorAll('.library-card, .playlist-card, .liked-song-card, .artist-card');
        freshCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.handleCardClick(e, card);
            });
            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleCardClick(e, card);
                }
            });
        });
        this.setupIntersectionObserver();
    }

    initializeParticles() {
        const particles = document.querySelectorAll('.particles-container .particle');
        if (particles.length === 0) return;

        particles.forEach((particle, index) => {
            // Random position within the container (avoid edges)
            particle.style.top = `${Math.random() * 80 + 10}%`;
            particle.style.left = `${Math.random() * 80 + 10}%`;

            // Staggered animation delay for smoother effect
            particle.style.animationDelay = `${(index * 0.8) + Math.random() * 2}s`;

            // Consistent duration with slight variation
            const baseDuration = this.isMobile ? 12 : 10;
            const duration = baseDuration + Math.random() * 4;
            particle.style.animationDuration = `${duration}s`;

            // Random drift direction and distance (more subtle)
            const angle = Math.random() * 2 * Math.PI;
            const maxDistance = this.isMobile ? 15 : 20;
            const distance = 10 + Math.random() * maxDistance;
            const dx = Math.cos(angle) * distance;
            const dy = Math.sin(angle) * distance;
            particle.style.setProperty('--dx', `${dx}px`);
            particle.style.setProperty('--dy', `${dy}px`);

            // More subtle color variety
            const colors = [
                'rgba(111,0,255,0.4)',
                'rgba(0,224,255,0.3)',
                'rgba(255,0,110,0.3)',
                'rgba(138,43,226,0.3)'
            ];
            const color = colors[index % colors.length];
            particle.style.background = `radial-gradient(circle, ${color}, transparent 60%)`;

            // Ensure animation properties are set
            particle.style.animationName = 'particleDrift';
            particle.style.animationTimingFunction = 'ease-in-out';
            particle.style.animationIterationCount = 'infinite';
            particle.style.animationFillMode = 'both';
        });
    }

    animateStats() {
        this.statNumbers.forEach((stat, index) => {
            const finalValue = stat.textContent;
            const numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
            const suffix = finalValue.replace(/[\d.]/g, '');

            if (!isNaN(numericValue)) {
                // Animate from 0 to final value
                let currentValue = 0;
                const increment = numericValue / 60;
                const duration = 2000; // 2 seconds
                const stepTime = duration / 60;

                setTimeout(() => {
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= numericValue) {
                            currentValue = numericValue;
                            clearInterval(timer);
                        }

                        if (suffix.includes('h')) {
                            stat.textContent = Math.floor(currentValue) + suffix;
                        } else {
                            stat.textContent = Math.floor(currentValue) + suffix;
                        }
                    }, stepTime);
                }, index * 300); // Stagger animations
            }
        });
    }

    setupIntersectionObserver() {
        const cards = document.querySelectorAll('.library-card, .playlist-card, .liked-song-card, .artist-card');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }

    handleSearch(query) {
        // Show/hide clear button
        if (query.trim()) {
            this.searchClear?.classList.remove('hidden');
        } else {
            this.searchClear?.classList.add('hidden');
        }

        // Debounce search
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        this.searchTimeout = setTimeout(() => {
            this.performSearch(query.trim());
        }, 300);
    }

    // Enhanced search with backend integration and Deezer fallback
    async performSearch(query) {
        if (!query) {
            this.showAllCards();
            this.clearSearchResults();
            return;
        }

        try {
            // Call backend search API
            const response = await fetch(`${this.apiBase}/library/search?q=${encodeURIComponent(query)}`);
            if (response.ok) {
                const searchResults = await response.json();
                this.displaySearchResults(searchResults, query);
            } else {
                throw new Error('Backend search failed');
            }
        } catch (error) {
            console.warn('Backend search failed, trying Deezer search:', error);
            await this.performDeezerSearch(query);
        }
    }

    // Deezer search integration
    async performDeezerSearch(query) {
        try {
            console.log(`🎵 Searching Deezer for: "${query}" with filter: ${this.currentFilter}`);

            let searchType = 'track';
            if (this.currentFilter === 'artists') searchType = 'artist';
            else if (this.currentFilter === 'albums') searchType = 'album';
            else if (this.currentFilter === 'playlists') searchType = 'playlist';

            const data = await this.deezerService.search(query, searchType, 20);

            if (data && data.data && data.data.length > 0) {
                const formattedResults = this.formatDeezerResults(data.data, searchType);
                this.displayDeezerResults(formattedResults, query, searchType);
                console.log(`✅ Found ${data.data.length} results from Deezer`);
            } else {
                // Fallback to client-side search
                this.performClientSideSearch(query);
            }
        } catch (error) {
            console.error('❌ Deezer search failed:', error);
            // Fallback to client-side search
            this.performClientSideSearch(query);
        }
    }

    formatDeezerResults(results, type) {
        return results.map(item => {
            switch (type) {
                case 'track':
                    return {
                        id: `deezer_track_${item.id}`,
                        type: 'song',
                        title: item.title,
                        artist: item.artist,
                        album: item.album,
                        image: item.cover,
                        preview: item.preview,
                        duration: this.formatDuration(item.duration),
                        source: 'deezer'
                    };
                case 'artist':
                    return {
                        id: `deezer_artist_${item.id}`,
                        type: 'artist',
                        name: item.name,
                        image: item.picture,
                        fans: item.fans ? item.fans.toLocaleString() : '',
                        source: 'deezer'
                    };
                case 'album':
                    return {
                        id: `deezer_album_${item.id}`,
                        type: 'album',
                        title: item.title,
                        artist: item.artist,
                        image: item.cover,
                        trackCount: item.trackCount,
                        releaseDate: item.releaseDate,
                        source: 'deezer'
                    };
                case 'playlist':
                    return {
                        id: `deezer_playlist_${item.id}`,
                        type: 'playlist',
                        title: item.title,
                        description: item.description,
                        image: item.cover,
                        trackCount: item.trackCount,
                        creator: item.creator,
                        source: 'deezer'
                    };
                default:
                    return item;
            }
        });
    }

    formatDuration(seconds) {
        if (!seconds) return '';
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // Display Deezer search results
    displayDeezerResults(results, query, searchType) {
        this.hideAllCards();

        // Show search results container
        let searchContainer = document.querySelector('.search-results-container');
        if (!searchContainer) {
            searchContainer = document.createElement('div');
            searchContainer.className = 'search-results-container';
            document.querySelector('.library-container').appendChild(searchContainer);
        }

        searchContainer.innerHTML = `
            <div class="search-results-header">
                <h2>Search Results for "${query}" <span class="deezer-badge">via Deezer</span></h2>
                <p class="search-info">Found ${results.length} ${searchType}${results.length !== 1 ? 's' : ''}</p>
            </div>
            <div class="search-results-content">
                <div class="deezer-results-grid"></div>
            </div>
        `;

        searchContainer.style.display = 'block';

        // Render results based on type
        const container = searchContainer.querySelector('.deezer-results-grid');
        results.forEach(item => {
            const card = this.createDeezerResultCard(item);
            container.appendChild(card);
        });

        this.announceAction(`Found ${results.length} ${searchType}${results.length !== 1 ? 's' : ''} from Deezer for "${query}"`);
    }

    createDeezerResultCard(item) {
        const card = document.createElement('div');
        card.className = 'card deezer-result-card';
        card.dataset.source = 'deezer';
        card.dataset.id = item.id;

        let cardContent = '';

        switch (item.type) {
            case 'song':
                cardContent = `
                    <div class="img-container">
                        <img src="${item.image || 'imgs/album-01.png'}" alt="${item.title}" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" data-preview="${item.preview}" data-title="${item.title}" data-artist="${item.artist}" aria-label="Play ${item.title}">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>${item.title}</h3>
                            <p>${item.artist} • ${item.album}</p>
                            ${item.duration ? `<span class="duration">${item.duration}</span>` : ''}
                        </div>
                        <div class="card-actions">
                            <button class="action-btn primary" data-preview="${item.preview}">
                                <i class="fas fa-play"></i> Play
                            </button>
                            <button class="action-btn" onclick="this.closest('.card').querySelector('.play-button').click()">
                                <i class="fas fa-plus"></i> Add
                            </button>
                        </div>
                    </div>
                `;
                break;
            case 'artist':
                cardContent = `
                    <div class="img-container">
                        <img src="${item.image || 'imgs/artist.png'}" alt="${item.name}" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="View ${item.name}">
                                <i class="fas fa-user"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>${item.name}</h3>
                            <p>${item.fans ? `${item.fans} fans` : 'Artist'}</p>
                        </div>
                        <div class="card-actions">
                            <button class="action-btn primary">
                                <i class="fas fa-user"></i> View Artist
                            </button>
                        </div>
                    </div>
                `;
                break;
            case 'album':
                cardContent = `
                    <div class="img-container">
                        <img src="${item.image || 'imgs/album-01.png'}" alt="${item.title}" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="View ${item.title}">
                                <i class="fas fa-compact-disc"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>${item.title}</h3>
                            <p>${item.artist} • ${item.trackCount ? `${item.trackCount} tracks` : 'Album'}</p>
                        </div>
                        <div class="card-actions">
                            <button class="action-btn primary">
                                <i class="fas fa-compact-disc"></i> View Album
                            </button>
                        </div>
                    </div>
                `;
                break;
            case 'playlist':
                cardContent = `
                    <div class="img-container">
                        <img src="${item.image || 'imgs/playlist-01.png'}" alt="${item.title}" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="View ${item.title}">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>${item.title}</h3>
                            <p>${item.creator ? `by ${item.creator}` : 'Playlist'} • ${item.trackCount ? `${item.trackCount} tracks` : ''}</p>
                        </div>
                        <div class="card-actions">
                            <button class="action-btn primary">
                                <i class="fas fa-list"></i> View Playlist
                            </button>
                        </div>
                    </div>
                `;
                break;
        }

        card.innerHTML = cardContent;
        return card;
    }

    // Client-side search fallback
    performClientSideSearch(query) {
        const cards = document.querySelectorAll('.card');
        let matchCount = 0;

        cards.forEach(card => {
            const title = card.querySelector('h3')?.textContent.toLowerCase() || '';
            const subtitle = card.querySelector('p')?.textContent.toLowerCase() || '';

            if (title.includes(query.toLowerCase()) || subtitle.includes(query.toLowerCase())) {
                card.style.display = 'block';
                card.classList.add('search-match');
                matchCount++;
            } else {
                card.style.display = 'none';
                card.classList.remove('search-match');
            }
        });

        this.announceAction(`Found ${matchCount} results for "${query}"`);
    }

    // Display search results from backend
    displaySearchResults(results, query) {
        this.hideAllCards();

        // Show search results container
        let searchContainer = document.querySelector('.search-results-container');
        if (!searchContainer) {
            searchContainer = document.createElement('div');
            searchContainer.className = 'search-results-container';
            searchContainer.innerHTML = `
                <div class="search-results-header">
                    <h2>Search Results for "${query}"</h2>
                </div>
                <div class="search-results-content">
                    <div class="search-tracks-section">
                        <h3>Songs</h3>
                        <div class="search-tracks-grid"></div>
                    </div>
                    <div class="search-playlists-section">
                        <h3>Playlists</h3>
                        <div class="search-playlists-grid"></div>
                    </div>
                </div>
            `;
            document.querySelector('.library-container').appendChild(searchContainer);
        }

        searchContainer.style.display = 'block';

        // Render search results
        this.renderSearchTracks(results.tracks || []);
        this.renderSearchPlaylists(results.playlists || []);

        const totalResults = (results.tracks?.length || 0) + (results.playlists?.length || 0);
        this.announceAction(`Found ${totalResults} results for "${query}"`);
    }

    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
            this.searchClear?.classList.add('hidden');
            this.showAllCards();
            this.searchInput.focus();
            this.announceAction('Search cleared');
        }
    }

    showAllCards() {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.style.display = 'block';
            card.classList.remove('search-match');
        });
        this.clearSearchResults();
    }

    hideAllCards() {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.style.display = 'none';
        });
    }

    clearSearchResults() {
        const searchContainer = document.querySelector('.search-results-container');
        if (searchContainer) {
            searchContainer.style.display = 'none';
        }
    }

    renderSearchTracks(tracks) {
        const container = document.querySelector('.search-tracks-grid');
        if (!container) return;

        container.innerHTML = '';

        if (tracks.length === 0) {
            container.innerHTML = '<p class="no-results">No songs found</p>';
            return;
        }

        tracks.forEach(track => {
            const trackCard = document.createElement('div');
            trackCard.className = 'card search-track-card';
            trackCard.innerHTML = `
                <div class="img-container">
                    <img src="imgs/album-01.png" alt="${track.title}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-button" aria-label="Play ${track.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="text-content">
                        <h3>${track.title}</h3>
                        <p>${track.artist} • ${track.album}</p>
                    </div>
                    <button class="button like-btn" data-track-id="${track.id}">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
            `;
            container.appendChild(trackCard);
        });
    }

    renderSearchPlaylists(playlists) {
        const container = document.querySelector('.search-playlists-grid');
        if (!container) return;

        container.innerHTML = '';

        if (playlists.length === 0) {
            container.innerHTML = '<p class="no-results">No playlists found</p>';
            return;
        }

        playlists.forEach(playlist => {
            const playlistCard = document.createElement('div');
            playlistCard.className = 'card search-playlist-card';
            playlistCard.innerHTML = `
                <div class="img-container">
                    <img src="${playlist.cover || 'imgs/playlist-01.png'}" alt="${playlist.name}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-button" aria-label="Play ${playlist.name}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="text-content">
                        <h3>${playlist.name}</h3>
                        <p>${playlist.tracks.length} tracks</p>
                    </div>
                    <button class="button open-playlist-btn" data-playlist-id="${playlist.id}">
                        Open
                    </button>
                </div>
            `;
            container.appendChild(playlistCard);
        });
    }

    switchSection(section) {
        // Update active tab
        this.navTabs.forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`)?.classList.add('active');

        // Show/hide sections (if implemented)
        this.announceAction(`Switched to ${section} section`);
    }

    shuffleAll() {
        this.announceAction('Shuffling all music in your library');
        // Simulate shuffle functionality
        console.log('Shuffling all music...');
    }

    importMusic() {
        this.announceAction('Opening music import dialog');
        // Simulate file import
        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = true;
        input.accept = 'audio/*';
        input.onchange = (e) => {
            const files = e.target.files;
            this.announceAction(`Selected ${files.length} file(s) for import`);
        };
        input.click();
    }

    async createPlaylist() {
        const name = prompt('Enter a name for your new playlist:');
        if (!name) return;

        const description = prompt('Enter a description (optional):') || '';

        try {
            const res = await fetch('http://localhost:3001/api/library/playlists', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name, description })
            });
            if (res.ok) {
                this.announceAction('Playlist created!');
                this.loadBackendData(); // Use loadBackendData for consistency
            } else {
                this.announceAction('Failed to create playlist.');
            }
        } catch (e) {
            this.announceAction('Error creating playlist.');
        }
    }

    playAllLikedSongs() {
        this.announceAction('Playing all liked songs');
        // Simulate playing all liked songs
        console.log('Playing all liked songs...');
    }

    handleCardClick(_, card) {
        const cardType = card.classList.contains('playlist-card') ? 'playlist' :
                        card.classList.contains('liked-song-card') ? 'song' :
                        card.classList.contains('artist-card') ? 'artist' : 'library';

        const title = card.querySelector('h3, .playlist-title, .song-title')?.textContent || 'Unknown';

        this.announceAction(`Opening ${cardType}: ${title}`);

        switch(cardType) {
            case 'playlist':
                // Get playlist ID from edit/delete button or card data
                let playlistId = card.querySelector('.edit-playlist-btn, .delete-playlist-btn')?.getAttribute('data-id');
                if (!playlistId && card.dataset.id) playlistId = card.dataset.id;
                if (playlistId) this.openPlaylistModal(playlistId, title);
                break;
            case 'song':
                console.log(`Playing song: ${title}`);
                break;
            case 'artist':
                console.log(`Opening artist page: ${title}`);
                break;
            default:
                console.log(`Opening ${title}`);
        }
    }

    async openPlaylistModal(playlistId, playlistName) {
        const modal = document.getElementById('playlist-modal');
        const closeBtn = document.getElementById('close-playlist-modal');
        const titleElem = document.getElementById('modal-playlist-title');
        const trackList = document.getElementById('modal-track-list');
        titleElem.textContent = playlistName;
        trackList.innerHTML = '<li>Loading...</li>';
        modal.style.display = 'flex';
        // Fetch playlist details
        try {
            const res = await fetch(`http://localhost:3001/api/library/playlists/${playlistId}`);
            if (!res.ok) throw new Error('Not found');
            const playlist = await res.json();
            if (!playlist.tracks.length) {
                trackList.innerHTML = '<li>No songs in this playlist.</li>';
            } else {
                // Fetch track info for each track
                const tracksInfo = await Promise.all(playlist.tracks.map(id => this.fetchTrackInfo(id)));
                trackList.innerHTML = '';
                tracksInfo.forEach((track, i) => {
                    const li = document.createElement('li');
                    if (track) {
                        li.innerHTML = `<span>${track.title} <span style='color:#aaa'>by ${track.artist}</span></span> <button data-index='${i}' data-id='${playlist.tracks[i]}'>Remove</button>`;
                    } else {
                        li.innerHTML = `<span>Track ID ${playlist.tracks[i]}</span> <button data-index='${i}' data-id='${playlist.tracks[i]}'>Remove</button>`;
                    }
                    trackList.appendChild(li);
                });
                // Remove song from playlist
                trackList.querySelectorAll('button').forEach(btn => {
                    btn.addEventListener('click', async () => {
                        await fetch(`http://localhost:3001/api/library/playlists/${playlistId}/tracks/${btn.dataset.id}`, { method: 'DELETE' });
                        this.openPlaylistModal(playlistId, playlistName); // Refresh
                        this.fetchAndRenderLibrary(); // Update main view
                    });
                });
            }
        } catch (e) {
            trackList.innerHTML = '<li>Error loading playlist.</li>';
        }
        // Close modal
        closeBtn.onclick = () => { modal.style.display = 'none'; };
        closeBtn.onkeydown = (e) => { if (e.key === 'Enter' || e.key === ' ') modal.style.display = 'none'; };
        window.onclick = (e) => { if (e.target === modal) modal.style.display = 'none'; };
    }

    createLiveRegion() {
        const liveRegion = document.createElement('div');
        liveRegion.id = 'aria-live-region';
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.className = 'sr-only';
        document.body.appendChild(liveRegion);
        return liveRegion;
    }

    announceAction(message) {
        // Use shared utility
        window.bansheeUtils.announceAction(message);
    }

    // Method to update library statistics
    updateStats(stats) {
        const elements = {
            totalPlaylists: document.getElementById('totalPlaylists'),
            totalLikedSongs: document.getElementById('totalLikedSongs'),
            totalListeningTime: document.getElementById('totalListeningTime'),
            totalArtists: document.getElementById('totalArtists')
        };

        Object.keys(stats).forEach(key => {
            if (elements[key]) {
                elements[key].textContent = stats[key];
            }
        });
    }

    // Method to add shimmer loading effect
    addLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.add('loading-shimmer');
        });
    }

    removeLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.remove('loading-shimmer');
        });
    }

    async fetchAndRenderLibrary() {
        // Fetch playlists
        let playlists = [];
        let likedSongs = [];
        try {
            const playlistsRes = await fetch('http://localhost:3001/api/library/playlists');
            playlists = await playlistsRes.json();
        } catch (e) {
            console.error('Failed to fetch playlists:', e);
        }
        try {
            const likedRes = await fetch('http://localhost:3001/api/library/liked');
            likedSongs = await likedRes.json();
        } catch (e) {
            console.error('Failed to fetch liked songs:', e);
        }
        this.renderPlaylists(playlists);
        this.renderLikedSongs(likedSongs);
        this.updateStats({
            totalPlaylists: playlists.length,
            totalLikedSongs: likedSongs.length,
        });
    }

    // Helper: fetch track info by ID from our backend
    async fetchTrackInfo(trackId) {
        try {
            const response = await fetch(`${this.apiBase}/tracks/${trackId}`);
            if (response.ok) {
                return await response.json();
            }
            return null;
        } catch (error) {
            console.error('Error fetching track info:', error);
            return null;
        }
    }

    async renderPlaylists(playlists) {
        const container = document.querySelector('.library-cards.centered');
        if (!container) return;
        container.innerHTML = '';
        if (!playlists.length) {
            container.innerHTML = '<span class="empty-results">No playlists found.</span>';
            return;
        }
        for (const pl of playlists) {
            // Fetch track data for each track in the playlist
            let tracksInfo = [];
            if (pl.tracks && pl.tracks.length) {
                tracksInfo = await Promise.all(pl.tracks.map(id => this.fetchTrackInfo(id)));
            }
            const card = document.createElement('div');
            card.className = 'card playlist-card';
            card.tabIndex = 0;
            card.setAttribute('aria-label', `${pl.name} playlist, ${pl.tracks.length} tracks`);
            card.dataset.id = pl.id; // <-- Add this for modal logic
            // Show up to 3 track covers in the playlist card
            const covers = tracksInfo.filter(t => t && t.album && t.album.cover_small).slice(0,3).map(t => `<img src="${t.album.cover_small}" alt="${t.title} Cover" loading="lazy">`).join('');
            // Show up to 2 track titles/artists with preview buttons
            const trackList = tracksInfo.filter(t => t && t.title && t.artist).slice(0,2).map(t => `
                <div class='playlist-track'>
                    ${t.title} <span class='playlist-artist'>by ${t.artist.name}</span>
                    <button class="preview-btn" data-preview="${t.preview}" aria-label="Play preview"><i class="fas fa-play"></i></button>
                </div>
            `).join('');
            card.innerHTML = `
                <div class="img-container">
                    ${covers || `<img src='imgs/album-01.png' alt='${pl.name} Cover' loading='lazy'>`}
                </div>
                <div class="card-content">
                    <h3 class="playlist-title">${pl.name}</h3>
                    <div class="playlist-meta">
                        <span class="playlist-count"><i class="fas fa-music"></i> ${pl.tracks.length} tracks</span>
                        <button class="edit-playlist-btn" data-id="${pl.id}" aria-label="Edit playlist"><i class="fas fa-edit"></i></button>
                        <button class="delete-playlist-btn" data-id="${pl.id}" aria-label="Delete playlist"><i class="fas fa-trash"></i></button>
                    </div>
                    <div class="playlist-tracks-preview">${trackList || '<span class=\'playlist-empty\'>No tracks yet</span>'}</div>
                </div>
            `;
            container.appendChild(card);
        }
        // Add preview button event listeners for playlist tracks
        container.querySelectorAll('.playlist-track .preview-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const url = btn.getAttribute('data-preview');
                if (url) {
                    this.playPreview(url, btn);
                } else {
                    this.announceAction('No preview available.');
                }
            });
        });
        // Add edit event listeners
        container.querySelectorAll('.edit-playlist-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = btn.getAttribute('data-id');
                this.editPlaylist(id);
            });
        });
        // Add delete event listeners
        container.querySelectorAll('.delete-playlist-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const id = btn.getAttribute('data-id');
                if (confirm('Delete this playlist?')) {
                    this.deletePlaylist(id);
                }
            });
        });
        // After rendering:
        this.refreshCardEventBindings();
    }

    async editPlaylist(id) {
        const newName = prompt('Enter a new name for this playlist:');
        if (!newName) return;
        try {
            const res = await fetch(`http://localhost:3001/api/library/playlists/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name: newName })
            });
            if (res.ok) {
                this.announceAction('Playlist updated!');
                this.fetchAndRenderLibrary();
            } else {
                this.announceAction('Failed to update playlist.');
            }
        } catch (e) {
            this.announceAction('Error updating playlist.');
        }
    }

    async editPlaylist(id) {
        try {
            // Get current playlist data
            const res = await fetch(`http://localhost:3001/api/library/playlists/${id}`);
            if (!res.ok) throw new Error('Playlist not found');

            const playlist = await res.json();
            const newName = prompt('Enter new playlist name:', playlist.name);

            if (newName && newName !== playlist.name) {
                const updateRes = await fetch(`http://localhost:3001/api/library/playlists/${id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name: newName, description: playlist.description })
                });

                if (updateRes.ok) {
                    this.announceAction('Playlist updated!');
                    this.loadBackendData(); // Refresh the display
                } else {
                    this.announceAction('Failed to update playlist.');
                }
            }
        } catch (e) {
            this.announceAction('Error editing playlist.');
        }
    }

    async deletePlaylist(id) {
        try {
            const res = await fetch(`http://localhost:3001/api/library/playlists/${id}`, { method: 'DELETE' });
            if (res.ok) {
                this.announceAction('Playlist deleted!');
                this.loadBackendData(); // Use loadBackendData instead of fetchAndRenderLibrary
            } else {
                this.announceAction('Failed to delete playlist.');
            }
        } catch (e) {
            this.announceAction('Error deleting playlist.');
        }
    }

    async renderLikedSongs(likedSongs) {
        const container = document.querySelector('.library-cards.liked-songs-cards');
        if (!container) return;
        container.innerHTML = '';
        if (!likedSongs.length) {
            container.innerHTML = '<span class="empty-results">No liked songs yet.</span>';
            return;
        }
        // Fetch all track info in parallel
        const trackData = await Promise.all(likedSongs.map(id => this.fetchTrackInfo(id)));
        trackData.forEach((track, idx) => {
            const songId = likedSongs[idx];
            const card = document.createElement('div');
            card.className = 'card liked-song-card';
            card.tabIndex = 0;
            card.setAttribute('aria-label', track && track.title ? `${track.title} by ${track.artist.name}, liked` : `Song ID ${songId}, liked`);
            card.innerHTML = `
                <div class="img-container">
                    <img src="${track && track.album && track.album.cover_medium ? track.album.cover_medium : 'imgs/album-01.png'}" alt="${track && track.title ? track.title : 'Song'} Cover" loading="lazy">
                </div>
                <div class="card-content">
                    <h3 class="song-title">${track && track.title ? track.title : 'Song #' + songId}</h3>
                    <div class="artist-name">${track && track.artist ? track.artist : 'Artist Unknown'}</div>
                    <div class="song-duration">${track && track.duration ? track.duration : ''}</div>
                    <button class="preview-btn" aria-label="Play ${track ? track.title : 'song'}"><i class="fas fa-play"></i> Play</button>
                    <button class="remove-liked-btn" data-songid="${songId}" aria-label="Remove from liked"><i class="fas fa-trash"></i></button>
                </div>
            `;
            container.appendChild(card);
        });
        // Add preview button event listeners
        container.querySelectorAll('.preview-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const url = btn.getAttribute('data-preview');
                if (url) {
                    this.playPreview(url, btn);
                } else {
                    this.announceAction('No preview available.');
                }
            });
        });
        // Add like button event listeners
        container.querySelectorAll('.like-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const songId = parseInt(btn.getAttribute('data-songid'));
                this.likeSong(songId);
            });
        });
        // Add remove liked song event listeners
        container.querySelectorAll('.remove-liked-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const songId = btn.getAttribute('data-songid');
                if (confirm('Remove this song from liked?')) {
                    this.removeLikedSong(songId);
                }
            });
        });
        // After rendering:
        this.refreshCardEventBindings();
    }

    playPreview(url, btn) {
        // Remove any existing preview audio
        if (this._previewAudio) {
            this._previewAudio.pause();
            this._previewAudio = null;
        }
        // Create and play new audio
        const audio = new Audio(url);
        audio.volume = 0.8;
        audio.play();
        this._previewAudio = audio;
        this.announceAction('Playing preview...');
        // Optionally, visually indicate playing
        btn.classList.add('playing');
        audio.onended = () => {
            btn.classList.remove('playing');
        };
        audio.onerror = () => {
            btn.classList.remove('playing');
            this.announceAction('Preview failed to play.');
        };
    }

    async removeLikedSong(songId) {
        try {
            const res = await fetch(`http://localhost:3001/api/library/liked/${songId}`, { method: 'DELETE' });
            if (res.ok) {
                this.announceAction('Song removed from liked!');
                this.fetchAndRenderLibrary();
            } else {
                this.announceAction('Failed to remove song.');
            }
        } catch (e) {
            this.announceAction('Error removing song.');
        }
    }

    // Backend connectivity methods
    async loadBackendData() {
        try {
            console.log('🎵 Loading data from backend...');

            // Load all library data using shared utility
            const endpoints = [
                { url: '/library/playlists', fallback: [] },
                { url: '/library/liked', fallback: [] },
                { url: '/library/recent', fallback: [] },
                { url: '/library/stats', fallback: { totalTracks: 0, totalPlaylists: 0, totalListeningTime: 0 } },
                { url: '/library/artists', fallback: [] }
            ];

            const results = await window.bansheeUtils.fetchMultiple(endpoints);

            // Process results
            if (results[0].status === 'fulfilled') {
                this.renderBackendPlaylists(results[0].value);
            }
            if (results[1].status === 'fulfilled') {
                this.renderBackendLikedSongs(results[1].value);
            }
            if (results[2].status === 'fulfilled') {
                this.renderRecentlyPlayed(results[2].value);
            }
            if (results[3].status === 'fulfilled') {
                this.updateStatsFromBackend(results[3].value);
            }
            if (results[4].status === 'fulfilled') {
                this.renderTopArtists(results[4].value);
            }

            console.log('✅ Backend data loaded successfully!');
            this.showNotification('Library loaded successfully!', 'success');
        } catch (error) {
            console.error('❌ Error loading backend data:', error);
            this.showNotification('Unable to connect to server. Using demo data.', 'warning');
        }
    }

    renderBackendPlaylists(playlists) {
        // Use the existing library-cards container for playlists
        const container = document.querySelector('.library-cards');
        if (!container) return;

        // Clear existing playlist cards only
        const existingPlaylistCards = container.querySelectorAll('.playlist-card');
        existingPlaylistCards.forEach(card => card.remove());

        playlists.forEach(playlist => {
            const playlistCard = document.createElement('div');
            playlistCard.className = 'card playlist-card';
            playlistCard.innerHTML = `
                <div class="img-container">
                    <img src="${playlist.cover || 'imgs/playlist-01.png'}" alt="${playlist.name}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-button" aria-label="Play ${playlist.name}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="text-content">
                        <h3>${playlist.name}</h3>
                        <p>${playlist.tracks.length} tracks • ${playlist.description || 'Custom playlist'}</p>
                    </div>
                    <div class="card-actions">
                        <button type="button" class="button open-playlist-btn" data-playlist-id="${playlist.id}">
                            Open Playlist
                        </button>
                        <button type="button" class="edit-playlist-btn" data-id="${playlist.id}" title="Edit playlist">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="delete-playlist-btn" data-id="${playlist.id}" title="Delete playlist">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(playlistCard);
        });

        // Add event listeners for dynamically created buttons
        this.bindPlaylistCardEvents(container);

        console.log(`✅ Rendered ${playlists.length} playlists from backend`);
    }

    bindPlaylistCardEvents(container) {
        // Open playlist buttons
        container.querySelectorAll('.open-playlist-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const playlistId = btn.dataset.playlistId;
                const playlistName = btn.closest('.card').querySelector('h3').textContent;
                this.openPlaylistModal(playlistId, playlistName);
            });
        });

        // Edit playlist buttons
        container.querySelectorAll('.edit-playlist-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const playlistId = btn.dataset.id;
                this.editPlaylist(playlistId);
            });
        });

        // Delete playlist buttons
        container.querySelectorAll('.delete-playlist-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const playlistId = btn.dataset.id;
                const playlistName = btn.closest('.card').querySelector('h3').textContent;
                if (confirm(`Are you sure you want to delete "${playlistName}"?`)) {
                    this.deletePlaylist(playlistId);
                }
            });
        });

        // Play buttons
        container.querySelectorAll('.play-button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const playlistName = btn.closest('.card').querySelector('h3').textContent;
                this.announceAction(`Playing ${playlistName}`);
            });
        });
    }

    renderBackendLikedSongs(likedSongs) {
        // Use the existing liked-songs-cards container
        const container = document.querySelector('.liked-songs-cards');
        if (!container) return;

        // Clear existing content
        container.innerHTML = '';

        if (likedSongs.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-heart"></i>
                    <h3>No liked songs yet</h3>
                    <p>Start liking songs to see them here!</p>
                </div>
            `;
            return;
        }

        likedSongs.forEach(track => {
            const trackCard = document.createElement('div');
            trackCard.className = 'card liked-song-card';
            trackCard.innerHTML = `
                <div class="card-cover">
                    <img src="${track.cover}" alt="${track.title}" loading="lazy">
                    <div class="card-overlay">
                        <button type="button" class="play-btn" aria-label="Play ${track.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="card-info">
                    <h3 class="card-title">${track.title}</h3>
                    <p class="card-meta">${track.artist}</p>
                    <div class="card-actions">
                        <button type="button" class="unlike-btn" data-id="${track.id}">
                            <i class="fas fa-heart"></i> Unlike
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(trackCard);
        });

        // Update liked songs count
        this.updateStatNumber('liked', likedSongs.length);
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'warning' ? '#FF9800' : '#2196F3'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
        `;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            <span style="margin-left: 8px;">${message}</span>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => notification.remove(), 3000);
    }

    // New methods for enhanced backend functionality
    renderRecentlyPlayed(tracks) {
        const container = document.querySelector('.recently-played-cards');
        if (!container || !tracks.length) return;

        container.innerHTML = tracks.map(track => `
            <div class="card track-card" data-track-id="${track.id}">
                <div class="img-container">
                    <img src="${track.cover}" alt="${track.title}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-button" aria-label="Play ${track.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="text-content">
                        <h3>${track.title}</h3>
                        <p>${track.artist} • ${track.duration}</p>
                    </div>
                    <button class="button like-button" data-track-id="${track.id}">
                        <i class="fas fa-heart"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    updateStatsFromBackend(stats) {
        // Update the hero section stats with real data while preserving the HTML structure
        const statsElements = document.querySelectorAll('.library-stats .stat-number');
        if (statsElements.length >= 3) {
            // Animate the stat numbers
            this.animateStatNumber(statsElements[0], stats.totalPlaylists || 0);
            this.animateStatNumber(statsElements[1], stats.totalLikedSongs || 0);
            this.animateStatNumber(statsElements[2], 34); // Following count (static for demo)
        }

        console.log('📊 Stats updated:', stats);
    }

    animateStatNumber(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1000; // 1 second
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.round(startValue + (targetValue - startValue) * easeOutQuart);

            element.textContent = currentValue;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    applyFilter(filter) {
        const allCards = document.querySelectorAll('.card');

        allCards.forEach(card => {
            let shouldShow = false;

            switch (filter) {
                case 'all':
                    shouldShow = true;
                    break;
                case 'playlists':
                    shouldShow = card.classList.contains('playlist-card');
                    break;
                case 'songs':
                    shouldShow = card.classList.contains('liked-song-card') ||
                               card.classList.contains('recently-played-card');
                    break;
                case 'artists':
                    shouldShow = card.classList.contains('top-artist-card');
                    break;
                default:
                    shouldShow = true;
            }

            card.style.display = shouldShow ? 'block' : 'none';
        });

        this.announceAction(`Showing ${filter === 'all' ? 'all items' : filter}`);
    }

    renderTopArtists(artists) {
        const container = document.querySelector('.top-artists-cards');
        if (!container || !artists.length) return;

        container.innerHTML = artists.map((artist, index) => `
            <div class="card top-artist-card" data-artist="${artist}">
                <div class="img-container">
                    <img src="imgs/album-0${(index % 4) + 1}.png" alt="${artist}" loading="lazy">
                    <div class="play-overlay">
                        <button type="button" class="play-button" aria-label="Play ${artist}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="text-content">
                        <h3>${artist}</h3>
                        <p>Artist</p>
                    </div>
                    <button class="button follow-button">
                        <i class="fas fa-user-plus"></i> Follow
                    </button>
                </div>
            </div>
        `).join('');
    }

    // ===== MOBILE OPTIMIZATION METHODS =====
    setupMobileOptimizations() {
        // Responsive breakpoint detection
        window.addEventListener('resize', () => {
            this.isMobile = window.innerWidth <= 768;
            this.adjustLayoutForDevice();
        });

        // Touch event optimizations
        if ('ontouchstart' in window) {
            this.setupTouchOptimizations();
        }

        // Viewport height fix for mobile browsers
        this.setViewportHeight();
        window.addEventListener('resize', () => this.setViewportHeight());
    }

    setupTouchOptimizations() {
        const container = document.querySelector('.library-container');
        if (!container) return;

        // Improve touch scrolling
        container.style.webkitOverflowScrolling = 'touch';

        // Add touch feedback to cards
        const cards = document.querySelectorAll('.library-card, .playlist-card');
        cards.forEach(card => {
            card.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
            card.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
        });

        // Optimize search input for mobile
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('focus', () => {
                // Prevent zoom on iOS
                searchInput.style.fontSize = '16px';
            });
        }
    }

    handleTouchStart(e) {
        this.touchStartY = e.touches[0].clientY;
        e.currentTarget.classList.add('touch-active');
    }

    handleTouchEnd(e) {
        e.currentTarget.classList.remove('touch-active');
    }

    setViewportHeight() {
        // Fix for mobile viewport height issues
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    adjustLayoutForDevice() {
        const container = document.querySelector('.library-container');
        if (!container) return;

        if (this.isMobile) {
            container.classList.add('mobile-layout');
            this.optimizeForMobile();
        } else {
            container.classList.remove('mobile-layout');
            this.optimizeForDesktop();
        }
    }

    optimizeForMobile() {
        // Reduce animation complexity on mobile
        const cards = document.querySelectorAll('.library-card');
        cards.forEach(card => {
            card.style.transition = 'transform 0.2s ease';
        });

        // Simplify particle effects for performance
        this.reduceParticleComplexity();
    }

    optimizeForDesktop() {
        // Restore full animations on desktop
        const cards = document.querySelectorAll('.library-card');
        cards.forEach(card => {
            card.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
        });
    }

    setupLazyLoading() {
        if (!this.bansheeUtils) return;

        // Lazy load album covers
        this.bansheeUtils.createLazyLoader('.album-cover', (img) => {
            img.classList.add('loaded');
        });
    }

    reduceParticleComplexity() {
        // Reduce particle count on mobile for better performance
        const particleContainer = document.querySelector('.particles-container');
        if (particleContainer && this.isMobile) {
            const particles = particleContainer.querySelectorAll('.particle');
            particles.forEach((particle, index) => {
                if (index % 2 === 0) { // Keep every other particle
                    particle.style.display = 'none';
                } else {
                    // Simplify remaining particles
                    particle.style.animationDuration = '12s'; // Slower animation
                    particle.style.opacity = '0.1'; // More transparent
                }
            });
        }
    }

    // ===== GITHUB PAGES COMPATIBILITY METHODS =====

    initializeLocalStorage() {
        // Initialize demo data for GitHub Pages
        const existingData = localStorage.getItem(this.storageKey);
        if (!existingData) {
            const demoData = {
                playlists: [
                    {
                        id: 'demo-playlist-1',
                        name: 'My Favorites',
                        description: 'Demo playlist with favorite tracks',
                        tracks: ['demo-track-1', 'demo-track-2'],
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 'demo-playlist-2',
                        name: 'Chill Vibes',
                        description: 'Relaxing music for any time',
                        tracks: ['demo-track-3'],
                        createdAt: new Date().toISOString()
                    }
                ],
                likedSongs: ['demo-track-1', 'demo-track-3'],
                recentlyPlayed: ['demo-track-2', 'demo-track-1']
            };
            localStorage.setItem(this.storageKey, JSON.stringify(demoData));
        }
    }

    getLocalData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : { playlists: [], likedSongs: [], recentlyPlayed: [] };
        } catch (error) {
            console.error('Error reading local storage:', error);
            return { playlists: [], likedSongs: [], recentlyPlayed: [] };
        }
    }

    saveLocalData(data) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to local storage:', error);
            return false;
        }
    }

    // GitHub Pages compatible search using hybrid service
    async performGitHubPagesSearch(query) {
        try {
            console.log(`🎵 GitHub Pages search for: "${query}"`);

            if (this.hybridMusicService) {
                const results = await this.hybridMusicService.search(query, 'track', 20);
                if (results.data && results.data.length > 0) {
                    this.displaySearchResults(results.data, query);
                    return;
                }
            }

            // Fallback to demo data
            const demoResults = [
                {
                    id: 'search-demo-1',
                    title: `${query} - Demo Track`,
                    artist: 'Demo Artist',
                    album: 'Search Results',
                    cover: 'imgs/album-01.png',
                    source: 'demo'
                }
            ];
            this.displaySearchResults(demoResults, query);
        } catch (error) {
            console.error('GitHub Pages search failed:', error);
            this.showAllCards();
        }
    }
}

// Initialize the library manager when the page loads
document.addEventListener('DOMContentLoaded', async () => {
    window.libraryManager = new LibraryManager();
    await window.libraryManager.init(); // Call init first to set up particles and animations
    window.libraryManager.fetchAndRenderLibrary();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LibraryManager;
}